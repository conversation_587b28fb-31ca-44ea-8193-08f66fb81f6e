import os
import redis
import json
import time
import uuid
from datetime import datetime, timezone

# Redis connection details from environment variables or defaults
redis_host = os.getenv("REDIS_HOST", "redis") # Default to 'redis' service name for K8s
redis_port = int(os.getenv("REDIS_PORT", 6379))
redis_password = os.getenv("REDIS_PASSWORD", "")
redis_db = int(os.getenv("REDIS_DB", 0))

# Initialize Redis client globally
r = redis.Redis(host=redis_host, port=redis_port, db=redis_db, password=redis_password)

# Set a TTL for Redis keys (e.g., 24 hours for temporary population)
redis_ttl_seconds = int(os.getenv("REDIS_TTL_SECONDS", 24 * 3600)) # Default TTL of 24 hours

# Ping Redis to ensure connection
try:
    r.ping()
    print("Connected to Redis successfully at {}:{}!".format(redis_host, redis_port))
except redis.exceptions.ConnectionError as e:
    print(f"Error connecting to Redis: {e}")
    exit(1)

# Define the ModelProfile class, matching the enhanced struct in policy-manager/main.go
class ModelProfile:
    def __init__(self, id, name, backend_type, url, api_key=None,
                 expected_cost=0.0, expected_latency_ms=0,
                 capabilities=None, data_sensitivity_level="low",
                 cost_per_input_token=0.0, cost_per_output_token=0.0,
                 cpu_cost_per_hour=0.0, memory_cost_per_hour=0.0,
                 version="", owner="", status="active", documentation_url="", license="",
                 fine_tuning_details="", input_context_length=0, output_context_length=0,
                 training_data_info="", last_evaluated_at=None, evaluation_metrics=None,
                 compliance_tags=None, region="", provider="", description=""):
        self.id = id
        self.name = name
        self.backend_type = backend_type
        self.url = url
        self.api_key = api_key if api_key is not None else ""
        self.expected_cost = expected_cost
        self.expected_latency_ms = expected_latency_ms
        self.capabilities = capabilities if capabilities is not None else []
        self.data_sensitivity_level = data_sensitivity_level
        self.cost_per_input_token = cost_per_input_token
        self.cost_per_output_token = cost_per_output_token
        self.cpu_cost_per_hour = cpu_cost_per_hour
        self.memory_cost_per_hour = memory_cost_per_hour
        self.created_at = datetime.now(timezone.utc).isoformat()
        self.updated_at = datetime.now(timezone.utc).isoformat()

        # LLM Registry fields
        self.version = version
        self.owner = owner
        self.status = status
        self.documentation_url = documentation_url
        self.license = license
        self.fine_tuning_details = fine_tuning_details
        self.input_context_length = input_context_length
        self.output_context_length = output_context_length
        self.training_data_info = training_data_info
        # Handle None for last_evaluated_at more robustly by setting to None if not provided
        self.last_evaluated_at = last_evaluated_at.isoformat() if isinstance(last_evaluated_at, datetime) else last_evaluated_at
        self.evaluation_metrics = json.dumps(evaluation_metrics) if evaluation_metrics is not None else "{}"
        self.compliance_tags = compliance_tags if compliance_tags is not None else []
        self.region = region
        self.provider = provider
        self.description = description

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "backend_type": self.backend_type,
            "url": self.url,
            "api_key": self.api_key,
            "expected_cost": self.expected_cost,
            "expected_latency_ms": self.expected_latency_ms,
            "capabilities": self.capabilities,
            "data_sensitivity_level": self.data_sensitivity_level,
            "cost_per_input_token": self.cost_per_input_token,
            "cost_per_output_token": self.cost_per_output_token,
            "cpu_cost_per_hour": self.cpu_cost_per_hour,
            "memory_cost_per_hour": self.memory_cost_per_hour,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "version": self.version,
            "owner": self.owner,
            "status": self.status,
            "documentation_url": self.documentation_url,
            "license": self.license,
            "fine_tuning_details": self.fine_tuning_details,
            "input_context_length": self.input_context_length,
            "output_context_length": self.output_context_length,
            "training_data_info": self.training_data_info,
            "last_evaluated_at": self.last_evaluated_at,
            "evaluation_metrics": json.loads(self.evaluation_metrics),
            "compliance_tags": self.compliance_tags,
            "region": self.region,
            "provider": self.provider,
            "description": self.description
        }

    def to_json(self):
        return json.dumps(self.to_dict())

# Define the Policy class, matching the enhanced struct in policy-manager/main.go
class Policy:
    def __init__(self, name, description, criteria=None, action="", backend_id="", priority=100, rules=None,
                 metadata=None, rate_limit=0, budget=0.0,
                 effect="", subjects=None, resource_type="", resource_ids=None, permissions=None, status="active"):
        self.id = str(uuid.uuid4())
        self.name = name
        self.description = description
        # CRITICAL FIX: Criteria and rules will be serialized to JSON strings.
        # Default criteria to an empty JSON object {} if not provided.
        self.criteria = json.dumps(criteria) if criteria is not None else "{}"
        self.action = action
        self.backend_id = backend_id
        self.priority = priority
        self.rules = json.dumps(rules) if rules is not None else "{}"
        self.created_at = datetime.now(timezone.utc).isoformat()
        self.updated_at = datetime.now(timezone.utc).isoformat()
        self.metadata = json.dumps(metadata) if metadata is not None else "{}"
        self.rate_limit = rate_limit
        self.budget = budget

        # RBAC fields
        self.effect = effect
        self.subjects = subjects if subjects is not None else []
        self.resource_type = resource_type
        self.resource_ids = resource_ids if resource_ids is not None else []
        self.permissions = permissions if permissions is not None else []
        self.status = status

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "criteria": json.loads(self.criteria),
            "action": self.action,
            "backend_id": self.backend_id,
            "priority": self.priority,
            "rules": json.loads(self.rules),
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "metadata": json.loads(self.metadata),
            "rate_limit": self.rate_limit,
            "budget": self.budget,
            "effect": self.effect,
            "subjects": self.subjects,
            "resource_type": self.resource_type,
            "resource_ids": self.resource_ids,
            "permissions": self.permissions,
            "status": self.status
        }

    def to_json(self):
        return json.dumps(self.to_dict())

# NEW: Define the Prompt class, matching the Go struct
class Prompt:
    def __init__(self, id, name, version, content, description="", tags=None,
                 owner="", status="draft", metadata=None):
        self.id = id
        self.name = name
        self.version = version
        self.content = content
        self.description = description
        self.tags = tags if tags is not None else []
        self.owner = owner
        self.status = status
        self.metadata = json.dumps(metadata) if metadata is not None else "{}"
        self.created_at = datetime.now(timezone.utc).isoformat()
        self.updated_at = datetime.now(timezone.utc).isoformat()
        self.promptKey = f"{self.id}:{self.version}"

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "version": self.version,
            "content": self.content,
            "description": self.description,
            "tags": self.tags,
            "owner": self.owner,
            "status": self.status,
            "metadata": json.loads(self.metadata),
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    def to_json(self):
        return json.dumps(self.to_dict())

# NEW: Define the RoutingStrategy class, matching the AI Optimizer's expected format
class RoutingStrategy:
    def __init__(self, id, name, strategy, task_type="", policy_id="",
                 model_priorities=None, parallel_models=None, comparison_method="",
                 minimum_tier=0, enable_fallback=False, fallback_model_id="", priority=100):
        self.id = id
        self.name = name
        self.strategy = strategy  # "default", "cascade", "parallel", "hybrid"
        self.task_type = task_type
        self.policy_id = policy_id
        self.model_priorities = model_priorities if model_priorities is not None else []
        self.parallel_models = parallel_models if parallel_models is not None else []
        self.comparison_method = comparison_method
        self.enable_fallback = enable_fallback
        self.fallback_model_id = fallback_model_id
        self.priority = priority
        self.model_requirements = {
            "minimum_tier": minimum_tier
        }

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "strategy": self.strategy,
            "task_type": self.task_type,
            "policy_id": self.policy_id,
            "model_priorities": self.model_priorities,
            "parallel_models": self.parallel_models,
            "comparison_method": self.comparison_method,
            "enable_fallback": self.enable_fallback,
            "fallback_model_id": self.fallback_model_id,
            "priority": self.priority,
            "model_requirements": self.model_requirements
        }

    def to_json(self):
        return json.dumps(self.to_dict())


def populate_model_profiles():
    # Fetch API keys from environment variables securely
    openai_api_key = os.getenv('OPENAI_API_KEY', '')
    google_api_key = os.getenv('GOOGLE_API_KEY', '')
    anthropic_api_key = os.getenv('ANTHROPIC_API_KEY', '')
    llama3_api_key = os.getenv('LLAMA3_API_KEY', '')

    # Log warnings if API keys are missing for external models
    if not openai_api_key:
        print("Warning: OPENAI_API_KEY environment variable not set. External OpenAI models may fail.")
    if not google_api_key:
        print("Warning: GOOGLE_API_KEY environment variable not set. External Google models may fail.")
    if not anthropic_api_key:
        print("Warning: ANTHROPIC_API_KEY environment variable not set. External Anthropic models may fail.")
    if not llama3_api_key:
        print("Warning: LLAMA3_API_KEY environment variable not set. Llama3 classifier may fail if external.")


    model_profiles_data = []

    # --- EXTERNAL / REAL LLMs (as defined in test_models.sh) ---

    # OpenAI GPT-3.5 Turbo
    model_profiles_data.append(ModelProfile(
        id="gpt-3.5-turbo", # Matches test_models.sh requested model
        name="GPT-3.5 Turbo (External)",
        backend_type="openai-external",
        url="https://api.openai.com/v1/chat/completions",
        api_key=openai_api_key,
        expected_cost=0.0000005, # Example pricing per 1k tokens
        expected_latency_ms=200,
        cost_per_input_token=0.0000005,
        cost_per_output_token=0.0000015,
        capabilities=["chat", "text-completion", "code-generation"],
        version="0125",
        owner="OpenAI",
        status="active", # Set to active
        documentation_url="https://platform.openai.com/docs/models/gpt-3-5-turbo",
        license="Proprietary",
        input_context_length=16385,
        output_context_length=4096,
        provider="OpenAI",
        region="global",
        last_evaluated_at=datetime(2025, 5, 20, 10, 0, 0, tzinfo=timezone.utc),
        evaluation_metrics={"factuality_score": 0.85, "coherence_score": 0.90},
        description="OpenAI's GPT-3.5 Turbo for fast, general-purpose chat."
    ))

    # OpenAI GPT-4o Mini
    model_profiles_data.append(ModelProfile(
        id="gpt-4o-mini", # Matches test_models.sh requested model
        name="GPT-4o Mini (External)",
        backend_type="openai-external",
        url="https://api.openai.com/v1/chat/completions",
        api_key=openai_api_key,
        expected_cost=0.00000015,
        expected_latency_ms=180,
        cost_per_input_token=0.00000015,
        cost_per_output_token=0.0000006,
        capabilities=["chat", "text-completion", "code-generation", "vision"],
        version="2024-05-13",
        owner="OpenAI",
        status="active", # Set to active
        documentation_url="https://openai.com/index/gpt-4o-mini/",
        license="Proprietary",
        input_context_length=128000,
        output_context_length=4096,
        provider="OpenAI",
        region="global",
        last_evaluated_at=datetime(2025, 5, 25, 11, 30, 0, tzinfo=timezone.utc),
        evaluation_metrics={"accuracy": 0.92, "multimodality_score": 0.88},
        description="OpenAI's efficient GPT-4o Mini for high-volume tasks."
    ))

    # Google Gemini 1.0 Pro (Marked as inactive/deprecated)
    model_profiles_data.append(ModelProfile(
        id="gemini-pro", # This is the ID explicitly requested in testmodels.sh
        name="Gemini 1.0 Pro (External, Deprecated)",
        backend_type="google-external",
        url="https://generativelanguage.googleapis.com/v1beta/models/gemini-1.0-pro:generateContent", # Keep old URL for reference but will be inactive
        api_key=google_api_key,
        expected_cost=0.000000125,
        expected_latency_ms=250,
        cost_per_input_token=0.000000125,
        cost_per_output_token=0.000000375,
        capabilities=["chat", "multi-modal", "text-completion", "code-generation"],
        version="001",
        owner="Google",
        status="inactive", # <--- CRITICAL CHANGE: Set to INACTIVE
        documentation_url="https://ai.google.dev/models/gemini",
        license="Proprietary",
        input_context_length=30720,
        output_context_length=2048,
        provider="Google",
        region="us-central1",
        compliance_tags=["GDPR"],
        last_evaluated_at=datetime(2025, 5, 18, 9, 0, 0, tzinfo=timezone.utc),
        evaluation_metrics={"creativity_score": 0.88, "reasoning_score": 0.82},
        description="Google Gemini 1.0 Pro model, now deprecated."
    ))

    # Google Gemini 1.5 Flash (Marked as inactive/deprecated)
    model_profiles_data.append(ModelProfile(
        id="gemini-1.5-flash",
        name="Gemini 1.5 Flash (External, Deprecated)",
        backend_type="google-external",
        url="https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent",
        api_key=google_api_key,
        expected_cost=0.00000035,
        expected_latency_ms=100,
        cost_per_input_token=0.00000035,
        cost_per_output_token=0.00000105,
        capabilities=["chat", "multi-modal", "text-completion", "code-generation", "long-context", "fast-inference"],
        version="001",
        owner="Google",
        status="inactive", # <--- CRITICAL CHANGE: Set to INACTIVE
        documentation_url="https://ai.google.dev/models/gemini",
        license="Proprietary",
        input_context_length=1048576,
        output_context_length=8192,
        provider="Google",
        region="global",
        compliance_tags=["GDPR"],
        last_evaluated_at=datetime.now(timezone.utc),
        evaluation_metrics={"factuality_score": 0.90, "coherence_score": 0.92, "speed_score": 0.98},
        description="Google Gemini 1.5 Flash model, now deprecated."
    ))

    # NEW: Google Gemini 2.5 Flash Preview 05-20 (Current recommendation for synthetic data generation)
    model_profiles_data.append(ModelProfile(
        id="gemini-2.5-flash-preview-05-20", # New ID for the latest Flash preview
        name="Gemini 2.5 Flash Preview (External)",
        backend_type="google-external",
        url="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent",
        api_key=google_api_key, # Uses the GOOGLE_API_KEY
        expected_cost=0.0000003, # Adjusted cost based on search results (approx $0.15/M input, $0.60/M output)
        expected_latency_ms=90, # Expected latency for 2.5 Flash Preview
        cost_per_input_token=0.0000003, # Example value
        cost_per_output_token=0.0000006, # Example value
        capabilities=["chat", "multi-modal", "text-completion", "code-generation", "long-context", "fast-inference", "thinking"], # Added 'thinking' capability
        version="05-20-preview",
        owner="Google DeepMind",
        status="active", # <--- Ensure this is ACTIVE
        documentation_url="https://ai.google.dev/gemini-api/docs/models",
        license="Proprietary",
        input_context_length=1048576, # 1M context window
        output_context_length=65536, # Higher output context for 2.5 Flash
        provider="Google",
        region="global",
        compliance_tags=["GDPR"],
        last_evaluated_at=datetime.now(timezone.utc),
        evaluation_metrics={"factuality_score": 0.91, "coherence_score": 0.93, "speed_score": 0.98, "reasoning_score": 0.85},
        description="Google Gemini 2.5 Flash Preview, suitable for synthetic data generation and general high-performance tasks."
    ))

    # Anthropic Claude 3 Haiku
    model_profiles_data.append(ModelProfile(
        id="claude-3-haiku", # Matches test_models.sh requested model
        name="Claude 3 Haiku (External)",
        backend_type="anthropic-external",
        url="https://api.anthropic.com/v1/messages",
        api_key=anthropic_api_key,
        expected_cost=0.00000025,
        expected_latency_ms=300,
        cost_per_input_token=0.00000025,
        cost_per_output_token=0.00000125,
        capabilities=["chat", "text-completion", "summarization", "long-context"],
        version="20240307",
        owner="Anthropic",
        status="active", # Set to active
        documentation_url="https://www.anthropic.com/news/claude-3-haiku",
        license="Proprietary",
        input_context_length=200000,
        output_context_length=4096,
        provider="Anthropic",
        region="global",
        compliance_tags=["SOC2"],
        last_evaluated_at=datetime(2025, 5, 22, 14, 0, 0, tzinfo=timezone.utc),
        evaluation_metrics={"summarization_quality": 0.91, "safety_score": 0.95},
        description="Anthropic's Claude 3 Haiku, known for speed and cost-effectiveness."
    ))

    # Custom Model A (example: internally fine-tuned OpenAI compatible model)
    model_profiles_data.append(ModelProfile(
        id="custom-model-a", # Matches test_models.sh requested model
        name="Custom Finance Model A",
        backend_type="openai", # Assumed to be deployed internally as OpenAI compatible
        url="http://mock-backend-gpu2:5002/v1/chat/completions", # Or your actual internal endpoint
        api_key="", # Internal mock, no API key needed
        expected_cost=0.000003,
        expected_latency_ms=200,
        cost_per_input_token=0.000003,
        cost_per_output_token=0.000007,
        capabilities=["financial-analysis", "report-generation"],
        version="1.0.1-finetune",
        owner="Finance AI Team",
        status="active", # Set to active
        documentation_url="http://internal-wiki/custom-model-a",
        license="Internal Use",
        fine_tuning_details="Fine-tuned on 10K financial reports",
        input_context_length=4096,
        output_context_length=1024,
        training_data_info="Proprietary financial documents, Q1 2024",
        last_evaluated_at=datetime(2025, 5, 15, 9, 30, 0, tzinfo=timezone.utc),
        evaluation_metrics={"accuracy_finance": 0.95, "hallucination_rate": 0.01},
        compliance_tags=["Internal-Compliance", "Data-Sensitive"],
        region="us-west1",
        provider="Internal",
        description="An internal model optimized for financial text analysis."
    ))

    # Another Model B (example: internally fine-tuned Google compatible model)
    model_profiles_data.append(ModelProfile(
        id="another-model-b", # Matches test_models.sh requested model
        name="Customer Service Chatbot B",
        backend_type="google", # Assumed to be deployed internally as Google compatible
        url="http://mock-google:5004/v1/models/gemini-1.0-pro:generateContent", # Or your actual internal endpoint
        api_key="", # Internal mock, no API key needed
        expected_cost=0.0000004,
        expected_latency_ms=180,
        cost_per_input_token=0.0000004,
        cost_per_output_token=0.0000009,
        capabilities=["customer-support", "FAQ-answering"],
        version="2.0-cs-alpha",
        owner="Customer Success AI",
        status="active", # Set to active
        documentation_url="http://internal-wiki/cs-chatbot-b",
        license="Internal Use",
        fine_tuning_details="Fine-tuned on 50K customer support transcripts",
        input_context_length=8192,
        output_context_length=2048,
        training_data_info="Anonymized customer interactions, H2 2024",
        last_evaluated_at=datetime(2025, 5, 28, 16, 0, 0, tzinfo=timezone.utc),
        evaluation_metrics={"resolution_rate": 0.80, "customer_satisfaction_score": 4.2},
        compliance_tags=["PII-redaction"],
        region="eu-central1",
        provider="Internal",
        description="An experimental chatbot for initial customer interactions."
    ))

    # --- INTERNAL MOCK LLMs (for testing / fallback without specific model name mapping) ---

    # Mock Backend GPU1 (Your internal OpenAI-compatible mock, used by default-backend-id)
    model_profiles_data.append(ModelProfile(
        id="mock-backend-gpu1",
        name="Mock Backend GPU1 (Generic OpenAI Mock)",
        backend_type="openai",
        url="http://mock-backend-gpu1:5001/v1/chat/completions",
        expected_cost=0.0001, expected_latency_ms=50, capabilities=["chat", "text-completion"],
        cost_per_input_token=0.0000001, cost_per_output_token=0.0000002,
        version="1.0-mock", owner="Internal Dev Team", status="active",
        documentation_url="http://internal-docs/mock-gpu1",
        provider="Internal", region="us-central1",
        last_evaluated_at=datetime.now(timezone.utc), # Add default timestamp
        description="A mock backend simulating an OpenAI-compatible GPU model."
    ))

    # Mock Backend GPU2 (Another internal OpenAI-compatible mock)
    model_profiles_data.append(ModelProfile(
        id="mock-backend-gpu2",
        name="Mock Backend GPU2 (Generic OpenAI Mock)",
        backend_type="openai",
        url="http://mock-backend-gpu2:5002/v1/chat/completions",
        expected_cost=0.00012, expected_latency_ms=60, capabilities=["chat", "text-completion"],
        cost_per_input_token=0.00000012, cost_per_output_token=0.00000025,
        version="1.0-mock", owner="Internal Dev Team", status="active",
        documentation_url="http://internal-docs/mock-gpu2",
        provider="Internal", region="us-central1",
        last_evaluated_at=datetime.now(timezone.utc), # Add default timestamp
        description="A mock backend simulating another OpenAI-compatible GPU model."
    ))

    # Mock Anthropic Backend (Your internal Anthropic-compatible mock)
    model_profiles_data.append(ModelProfile(
        id="mock-anthropic",
        name="Mock Anthropic (Generic Anthropic Mock)",
        backend_type="anthropic",
        url="http://mock-anthropic:5003/v1/messages",
        expected_cost=0.0000003, expected_latency_ms=150, capabilities=["chat", "summarization"],
        cost_per_input_token=0.0000001, cost_per_output_token=0.0000002,
        version="1.0-mock", owner="Internal QA Team", status="active",
        documentation_url="http://internal-docs/mock-anthropic",
        provider="Internal", region="us-central1",
        last_evaluated_at=datetime.now(timezone.utc), # Add default timestamp
        description="A mock backend simulating an Anthropic-compatible model."
    ))

    # --- CLASSIFIER MODELS (Prioritized in AI Optimizer) ---

    # Primary Classifier: Google Gemini Flash (already uses gemini-2.0-flash)
    model_profiles_data.append(ModelProfile(
        id="gemini-1.0-pro-classifier", # This ID is used internally by ai-optimizer
        name="Gemini Flash Classifier", # Name updated to reflect Flash
        backend_type="google-external", # Use google-external if using real API
        url="https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
        api_key=google_api_key, # Uses the GOOGLE_API_KEY
        expected_cost=0.0000001, # Example pricing for Flash (generally lower than Pro)
        expected_latency_ms=80,  # Example latency for Flash (generally faster)
        cost_per_input_token=0.0000001,
        cost_per_output_token=0.0000002,
        capabilities=["text-classification", "fast-inference"], # Added fast-inference capability
        version="2.0-flash", owner="AI Ops Team", status="active", # Version and status updated
        documentation_url="https://ai.google.dev/models/gemini",
        fine_tuning_details="Pre-trained for general classification tasks, optimized for speed",
        input_context_length=131072, output_context_length=8192, # Updated context lengths for Flash
        training_data_info="General web data, optimized for speed",
        last_evaluated_at=datetime.now(timezone.utc),
        evaluation_metrics={"accuracy": 0.93, "f1_score": 0.90, "speed_score": 0.95}, # Added speed_score
        compliance_tags=["data-privacy-compliant", "high-throughput"],
        region="global", provider="Google",
        description="Internal Gemini Flash model optimized for content classification."
    ))

    # Secondary Classifier: Llama3 (assuming VLLM or similar deployment)
    model_profiles_data.append(ModelProfile(
        id="llama3-classifier", # Matches AI Optimizer's secondary classifier ID
        name="Llama3 8B Classifier",
        backend_type="vllm", # Or "openai-external" if it's an OpenAI-compatible API
        url="http://your-llama3-vllm-service:8000/v1/chat/completions", # <--- REPLACE THIS URL!
        api_key=llama3_api_key, # Uses the LLAMA3_API_KEY
        expected_cost=0.000000005,
        expected_latency_ms=80,
        cost_per_input_token=0.000000005,
        cost_per_output_token=0.000000007,
        capabilities=["text-classification", "summarization"],
        version="8B-Instruct-v2.0", owner="Internal ML Team",
        status="active", # Change to "active" for testing purposes
        documentation_url="https://huggingface.co/meta-llama/Meta-Llama-3-8B-Instruct",
        fine_tuning_details="Finetuned on custom classification dataset",
        input_context_length=8192, output_context_length=2048,
        training_data_info="Mixture of internal documents and public datasets",
        last_evaluated_at=datetime.now(timezone.utc),
        evaluation_metrics={"accuracy": 0.90, "precision": 0.88},
        compliance_tags=["internal-only"], region="us-central1", provider="Meta",
        description="Locally hosted Llama3 8B model for classification tasks."
    ))

    # Last Fallback Classifier: Mock OpenAI
    model_profiles_data.append(ModelProfile(
        id="mock-openai-classifier", # Matches AI Optimizer's last fallback classifier ID
        name="Mock OpenAI Classifier (Internal Fallback)",
        backend_type="openai",
        url="http://mock-openai:5000/v1/chat/completions", # <--- REPLACE THIS URL if needed!
        api_key="", # No API key needed for this internal mock
        expected_cost=0.00000001,
        expected_latency_ms=120,
        cost_per_input_token=0.00000001,
        cost_per_output_token=0.00000015,
        capabilities=["text-classification"],
        version="1.0-mock", owner="Internal Dev Team",
        status="active", # Change to "active" for testing purposes
        documentation_url="http://internal-docs/mock-openai-classifier",
        provider="Internal", region="us-central1",
        last_evaluated_at=datetime.now(timezone.utc), # Add default timestamp
        description="A low-cost mock classifier for internal fallback scenarios."
    ))

    # --- ULTIMATE DEFAULT FALLBACK ---
    # This is the backend used if no other policies or direct model matches are found.
    model_profiles_data.append(ModelProfile(
        id="default-backend-id",
        name="Default Mock Backend GPU1",
        backend_type="openai",
        url="http://mock-backend-gpu1:5001/v1/chat/completions",
        expected_cost=0.0001, expected_latency_ms=50, capabilities=["chat"],
        cost_per_input_token=0.0000001, cost_per_output_token=0.0000002,
        version="1.0-default", owner="Platform Team", status="active",
        documentation_url="http://internal-docs/default-backend",
        provider="Internal", region="us-central1",
        last_evaluated_at=datetime.now(timezone.utc), # Add default timestamp
        description="The ultimate fallback backend if no other models are suitable or available."
    ))

    # A special model profile to represent "cache hit"
    model_profiles_data.append(ModelProfile(
        id="cache",
        name="Cache Hit",
        backend_type="cache",
        url="", # No external URL for cache
        expected_cost=0.000000001, # Extremely low cost for cache hits
        expected_latency_ms=1,    # Very low latency
        capabilities=["caching"],
        status="active",
        description="Represents a response served directly from cache."
    ))

    # A special model profile for the AI Optimizer's optimal route selection
    model_profiles_data.append(ModelProfile(
        id="ai-optimizer-optimal",
        name="AI Optimizer Default Route",
        backend_type="optimizer", # Custom backend type for the optimizer itself
        url="", # No direct URL
        expected_cost=0.0,
        expected_latency_ms=0,
        capabilities=["routing", "optimization"],
        status="active",
        description="Represents the AI Optimizer's decision to route to the optimal model."
    ))

    import requests
    policy_manager_url = "http://policy-manager:8083/api/model-profiles"
    headers = {'Content-type': 'application/json'}

    print("Populating initial model profiles by calling policy-manager...")
    for profile_obj in model_profiles_data:
        try:
            response = requests.post(policy_manager_url, data=profile_obj.to_json(), headers=headers)
            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            print(f"Successfully created model profile '{profile_obj.name}' with ID {profile_obj.id} via policy-manager.")
        except requests.exceptions.RequestException as e:
            print(f"Error creating model profile '{profile_obj.name}' with ID {profile_obj.id} via policy-manager: {e}")
    print("Finished populating initial model profiles.")


def populate_policies():
    policies_data = []

    # --- RBAC Policies (for future use, structure is aligned) ---

    # Policy: Allow 'developer' role to access 'gpt-4o-mini'
    policies_data.append(Policy(
        name="Allow Dev Access to GPT-4o Mini",
        description="Allows users with 'developer' role to access gpt-4o-mini.",
        effect="ALLOW",
        subjects=["role:developer"],
        resource_type="model",
        resource_ids=["gpt-4o-mini"],
        permissions=["access_llm"],
        priority=10,
        status="active",
        criteria={} # Explicitly empty object for RBAC policies
    ))

    # Policy: Deny 'guest' role access to ALL models
    policies_data.append(Policy(
        name="Deny Guest Access to All Models",
        description="Denies access to all models for users with 'guest' role.",
        effect="DENY",
        subjects=["role:guest"],
        resource_type="model",
        resource_ids=["*"],
        permissions=["access_llm"],
        priority=0,
        status="active",
        criteria={} # Explicitly empty object for RBAC policies
    ))

    # Policy: Deny specific user 'user-bob' access to 'claude-3-haiku'
    policies_data.append(Policy(
        name="Deny Bob Access to Claude 3 Haiku",
        description="Denies a specific user access to Claude 3 Haiku.",
        effect="DENY",
        subjects=["user-bob"],
        resource_type="model",
        resource_ids=["claude-3-haiku"],
        permissions=["access_llm"],
        priority=5,
        status="active",
        criteria={} # Explicitly empty object for RBAC policies
    ))

    # Policy: Allow all authenticated users to access 'gpt-3.5-turbo' (if not explicitly denied)
    policies_data.append(Policy(
        name="Allow All Authenticated to GPT-3.5 Turbo",
        description="Allows all authenticated users (with X-User-ID) to access gpt-3.5-turbo.",
        effect="ALLOW",
        subjects=["*"],
        resource_type="model",
        resource_ids=["gpt-3.5-turbo"],
        permissions=["access_llm"],
        priority=100,
        status="active",
        criteria={} # Explicitly empty object for RBAC policies
    ))

    # --- ROUTING/OPTIMIZATION Policies ---

    # Policy: High Data Sensitivity to Secure Backend
    policies_data.append(Policy(
        name="High Data Sensitivity to Secure Backend",
        description="Routes requests with high data sensitivity to a secure backend.",
        criteria={"data_sensitivity_level": "high"},
        action="ROUTE",
        backend_id="custom-model-a",
        priority=10,
        status="active"
    ))

    # Policy: Route requests for 'long-context' model to Google Gemini Flash (now 2.5 Flash)
    policies_data.append(Policy(
        name="Long Context to Google Gemini Flash",
        description="Routes requests specifically asking for 'long-context' capability to Gemini 2.5 Flash.",
        criteria={"requested_model": "long-context", "capability": "long-context"},
        action="ROUTE",
        backend_id="gemini-2.5-flash-preview-05-20",
        priority=20,
        status="active"
    ))

    # Policy: Route 'simple-chat' requests to the cheapest vLLM mock
    policies_data.append(Policy(
        name="Simple Chat to Cheapest vLLM",
        description="Routes 'simple-chat' requests to the cheapest vLLM mock.",
        criteria={"task_type": "simple_chat"},
        action="ROUTE",
        backend_id="mock-backend-gpu2",
        priority=30,
        status="active"
    ))

    # Policy: General optimization for cost for certain users/prompts (example)
    policies_data.append(Policy(
        name="Cost Optimization for General Queries",
        description="Optimizes routing for general queries based on cost preference.",
        criteria={"task_type": "factual_query"},
        action="OPTIMIZE",
        rules={"optimization_goal": "cost", "preferred_backends": ["gpt-3.5-turbo", "gemini-2.5-flash-preview-05-20"]},
        priority=40,
        status="active"
    ))

    # Example of an inactive policy
    policies_data.append(Policy(
        name="Deprecated Test Policy",
        description="This policy is deprecated and should not be active.",
        action="BLOCK",
        priority=999,
        status="inactive"
    ))
    import requests
    policy_manager_url = "http://policy-manager:8083/api/policies"
    headers = {'Content-type': 'application/json'}

    print("Populating initial policies by calling policy-manager...")
    for policy_obj in policies_data:
        try:
            response = requests.post(policy_manager_url, data=policy_obj.to_json(), headers=headers)
            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            print(f"Successfully created policy '{policy_obj.name}' with ID {policy_obj.id} via policy-manager.")
        except requests.exceptions.RequestException as e:
            print(f"Error creating policy '{policy_obj.name}' with ID {policy_obj.id} via policy-manager: {e}")
    print("Finished populating initial policies.")


def populate_prompts():
    prompts_data = []

    prompts_data.append(Prompt(
        id="summarization-template",
        name="Summarization Template",
        version="1.0",
        content="Please summarize the following text concisely:",
        description="A basic template for summarizing text.",
        tags=["text-summary", "template"],
        owner="Content Team",
        status="active",
        metadata={"category": "content-creation"}
    ))

    prompts_data.append(Prompt(
        id="code-generation-python",
        name="Python Code Generator",
        version="2.0",
        content="Generate a Python function that does the following: {description}",
        description="Template for generating Python code snippets.",
        tags=["code-gen", "python"],
        owner="Dev Tools Team",
        status="active",
        metadata={"language": "python"}
    ))

    prompts_data.append(Prompt(
        id="creative-story-starter",
        name="Creative Story Starter",
        version="1.0",
        content="Write a very short, imaginative story about: {topic}",
        description="A prompt template to kickstart creative writing.",
        tags=["creative-writing", "story"],
        owner="Marketing Team",
        status="draft",
        metadata={"genre": "fantasy"}
    ))

    prompts_data.append(Prompt(
        id="new-summary-template",
        name="New Summary Template",
        version="1.1-beta",
        content="Summarize the provided content for a technical audience, highlighting key findings:",
        description="An updated template for summarizing, focusing on conciseness.",
        tags=["text-summary", "template", "beta", "for-ab-test"],
        owner="Content Team",
        status="experimental",
        metadata={"category": "content-creation", "test_group": "beta-users"}
    ))
    import requests
    policy_manager_url = "http://policy-manager:8083/api/prompts"
    headers = {'Content-type': 'application/json'}

    print("Populating initial prompts by calling policy-manager...")
    for prompt_obj in prompts_data:
        try:
            prompt_json = prompt_obj.to_json()
            response = requests.post(policy_manager_url, data=prompt_json, headers=headers)
            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            print(f"Successfully created prompt '{prompt_obj.name}' with ID {prompt_obj.id} via policy-manager.")
        except requests.exceptions.RequestException as e:
            print(f"Error creating prompt '{prompt_obj.name}' with ID {prompt_obj.id} via policy-manager: {e}")
    print("Finished populating initial prompts.")


    # Publish initial population events to Redis Pub/Sub
    try:
        r.publish('model_profile_updates', 'initial_population')
        r.publish('policy_updates', 'initial_population')
        r.publish('prompt_updates', 'initial_population') # NEW: Publish prompt update event
        print("Published initial population events to Redis Pub/Sub.")
    except Exception as e:
        print(f"Error publishing to Redis Pub/Sub: {e}")


def populate_routing_strategies():
    """
    Create routing strategies that the AI Optimizer can understand.
    These are stored in Redis with keys like 'routing_strategy:*'
    """
    routing_strategies_data = []

    # Strategy 1: Default routing for factual queries to cost-effective models
    routing_strategies_data.append(RoutingStrategy(
        id="factual-query-default",
        name="Factual Query Default Strategy",
        strategy="default",
        task_type="factual_query",
        model_priorities=["gpt-3.5-turbo", "gemini-2.5-flash-preview-05-20", "mock-backend-gpu1"],
        minimum_tier=1
    ))

    # Strategy 2: Creative writing to more capable models
    routing_strategies_data.append(RoutingStrategy(
        id="creative-writing-default",
        name="Creative Writing Default Strategy",
        strategy="default",
        task_type="creative_writing",
        model_priorities=["gpt-4o-mini", "claude-3-haiku", "gemini-2.5-flash-preview-05-20"],
        minimum_tier=2
    ))

    # Strategy 3: Simple chat to cheapest available
    routing_strategies_data.append(RoutingStrategy(
        id="simple-chat-default",
        name="Simple Chat Default Strategy",
        strategy="default",
        task_type="simple_chat",
        model_priorities=["mock-backend-gpu1", "mock-backend-gpu2", "gpt-3.5-turbo"],
        minimum_tier=1
    ))

    # Strategy 4: Cascade strategy for high-priority requests
    routing_strategies_data.append(RoutingStrategy(
        id="high-priority-cascade",
        name="High Priority Cascade Strategy",
        strategy="cascade",
        task_type="high_priority",
        model_priorities=["gpt-4o-mini", "claude-3-haiku", "gpt-3.5-turbo", "mock-backend-gpu1"],
        minimum_tier=1
    ))

    # Strategy 5: Default fallback strategy (no specific task type)
    routing_strategies_data.append(RoutingStrategy(
        id="default-fallback",
        name="Default Fallback Strategy",
        strategy="default",
        task_type="",  # Empty means it applies to any task type
        model_priorities=["mock-backend-gpu1", "gpt-3.5-turbo"],
        minimum_tier=1
    ))

    # Store routing strategies directly in Redis (not via policy-manager API)
    print("Populating routing strategies directly in Redis...")
    for strategy_obj in routing_strategies_data:
        try:
            redis_key = f"routing_strategy:{strategy_obj.id}"
            strategy_json = strategy_obj.to_json()
            r.set(redis_key, strategy_json)
            print(f"Successfully stored routing strategy '{strategy_obj.name}' with ID {strategy_obj.id} in Redis.")
        except Exception as e:
            print(f"Error storing routing strategy '{strategy_obj.name}' with ID {strategy_obj.id} in Redis: {e}")
    print("Finished populating routing strategies.")


if __name__ == "__main__":
    populate_model_profiles()
    populate_policies()
    populate_prompts()
    populate_routing_strategies()  # NEW: Add routing strategies
    print("Redis population script finished.")
    print("You should see the initial data in Redis. Verify with 'redis-cli' if needed.")

