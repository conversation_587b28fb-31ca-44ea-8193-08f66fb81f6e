#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "Script execution started."
echo "Script is running from: ${SCRIPT_DIR}"

# Define the root directory of your project based on the script's location
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
echo "Project root: ${PROJECT_ROOT}"

# --- Configuration Variables for GCP and GKE ---
# These variables are passed as environment variables from cloudbuild.yaml
# and are used throughout the script for image paths and GCP commands.
echo "GCP_PROJECT_ID: ${GCP_PROJECT_ID}"
echo "GCP_REGION: ${GCP_REGION}"
echo "GKE_CLUSTER_NAME: ${GKE_CLUSTER_NAME}"
echo "ARTIFACT_REGISTRY_REPO: ${ARTIFACT_REGISTRY_REPO}"

# Full image paths (also passed as environment variables from cloudbuild.yaml)
echo "MOCK_BACKEND_GPU1_IMAGE: ${MOCK_BACKEND_GPU1_IMAGE}"
echo "MOCK_BACKEND_GPU2_IMAGE: ${MOCK_BACKEND_GPU2_IMAGE}"
echo "MOCK_OPENAI_IMAGE: ${MOCK_OPENAI_IMAGE}"
echo "MOCK_GOOGLE_IMAGE: ${MOCK_GOOGLE_IMAGE}"
echo "MOCK_ANTHROPIC_IMAGE: ${MOCK_ANTHROPIC_IMAGE}"
echo "PROXY_GATEWAY_IMAGE: ${PROXY_GATEWAY_IMAGE}"
echo "DATA_PROCESSOR_IMAGE: ${DATA_PROCESSOR_IMAGE}"
echo "KAFKA_TOPIC_CREATOR_IMAGE: ${KAFKA_TOPIC_CREATOR_IMAGE}"
echo "DASHBOARD_API_IMAGE: ${DASHBOARD_API_IMAGE}"
echo "POLICY_MANAGER_IMAGE: ${POLICY_MANAGER_IMAGE}"
echo "AI_OPTIMIZER_IMAGE: ${AI_OPTIMIZER_IMAGE}"
echo "FRONTEND_IMAGE: ${FRONTEND_IMAGE}"
echo "EVALUATION_SERVICE_IMAGE: ${EVALUATION_SERVICE_IMAGE}"
echo "GOVERNANCE_SERVICE_IMAGE: ${GOVERNANCE_SERVICE_IMAGE}"
echo "INTEGRATION_SERVICE_IMAGE: ${INTEGRATION_SERVICE_IMAGE}"

# --- GKE Context Setup (Minimal for Cloud Build) ---
# In Cloud Build, gcloud container clusters get-credentials is usually run as a separate step
# and sets the kubectl context. This section is mostly for verification or local runs.
echo "--- Configuring gcloud and kubectl for GKE ---"
gcloud config set project "${GCP_PROJECT_ID}"
gcloud config set compute/region "${GCP_REGION}"
echo "Verifying kubectl context..."
kubectl config current-context
echo "--- GKE Context Setup Complete ---"

# --- Artifact Registry Setup (Minimal for Cloud Build) ---
# Cloud Build's docker builder automatically uses Artifact Registry if configured.
echo "--- Setting up Google Artifact Registry ---"
echo "--- Artifact Registry Setup Complete (if pre-configured) ---"


# --- Comprehensive Cleanup of Previous Kubernetes Deployment ---
echo "--- Cleaning up previous Kubernetes deployment (if any) ---"

# --- Drop ClickHouse Table (per initial plan) ---
# This is a separate script that handles dropping the ClickHouse table.
echo "--- Dropping ClickHouse table 'inference_logs' for a clean slate ---"
# Make sure drop_clickhouse_table.sh is executable and has correct paths
# Path confirmed: k8s/scripts/drop_clickhouse_table.sh
"${SCRIPT_DIR}/scripts/drop_clickhouse_table.sh" || echo "Warning: drop_clickhouse_table.sh failed or was not found. Continuing."
echo "ClickHouse table drop attempt completed via drop_clickhouse_table.sh."


# Define the list of Kubernetes YAML files to delete resources from
MANIFESTS_TO_DELETE=(
  "${SCRIPT_DIR}/clickhouse/clickhouse-k8s.yaml"
  "${SCRIPT_DIR}/kafka/kafka-zookeeper-k8s.yaml"
  "${SCRIPT_DIR}/kafka_topic_creator/kafka-topic-creator-job.yaml"
  "${SCRIPT_DIR}/data-processor/data-processor.yaml"
  "${SCRIPT_DIR}/proxy-gateway/proxy-gateway.yaml"
  "${SCRIPT_DIR}/prometheus/prometheus-k8s.yaml"
  "${SCRIPT_DIR}/dashboard-api/dashboard-api.yaml"
  "${SCRIPT_DIR}/frontend/frontend.yaml"
  "${SCRIPT_DIR}/mock_backend/mock-backend-gpu1.yaml"
  "${SCRIPT_DIR}/mock_backend/mock-backend-gpu2.yaml"
  "${SCRIPT_DIR}/mock_backend/mock-openai.yaml"
  "${SCRIPT_DIR}/mock_backend/mock-google.yaml"
  "${SCRIPT_DIR}/mock_backend/mock-anthropic.yaml"
  "${SCRIPT_DIR}/kafka/kafka-server-properties-configmap.yaml"
  "${SCRIPT_DIR}/kafka/kafka-log4j-configmap.yaml"
  "${SCRIPT_DIR}/redis/redis-k8s.yaml"
  "${SCRIPT_DIR}/policy-manager/policy-manager-k8s.yaml"
  "${SCRIPT_DIR}/ai-optimizer/ai-optimizer.yaml"
  "${SCRIPT_DIR}/evaluation-service/evaluation-service.yaml"
)

echo "Attempting to delete existing Kubernetes resources from manifests..."
for manifest in "${MANIFESTS_TO_DELETE[@]}"; do
    if [ -f "$manifest" ]; then
        echo "Deleting resources from $manifest..."
        kubectl delete -f "$manifest" --ignore-not-found --force --grace-period=0 || {
            echo "Warning: Failed to delete resources from $manifest. Check kubectl output above."
        }
    else
        echo "Warning: Manifest file not found for deletion: $manifest. Skipping deletion for this file."
    fi
done

# Also attempt to delete specific resources by name if they are not covered by manifests
echo "Attempting to delete specific secrets and configmaps by name..."
kubectl delete secret clickhouse-credentials --ignore-not-found=true
kubectl delete secret gcp-service-account-key --ignore-not-found=true # If you still use this directly
kubectl delete secret openai-api-key --ignore-not-found=true
kubectl delete secret google-api-key --ignore-not-found=true
kubectl delete secret anthropic-api-key --ignore-not-found=true
kubectl delete configmap proxy-gateway-config --ignore-not-found=true
kubectl delete configmap frontend-nginx-config --ignore-not-found=true # Ensure Nginx config is also deleted
echo "Attempted deletion of specified Kubernetes resources."

echo "--- Cleanup Complete ---"


# --- Workload Identity Setup ---
echo "--- Setting up Workload Identity for Policy Manager ---"

POLICY_MANAGER_KSA_NAME="policy-manager-ksa"
POLICY_MANAGER_GSA_NAME="policy-manager-gsa"

echo "Creating Google Service Account ${POLICY_MANAGER_GSA_NAME}..."
if ! gcloud iam service-accounts describe "${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
    gcloud iam service-accounts create "${POLICY_MANAGER_GSA_NAME}" \
        --display-name="Service Account for Policy Manager"
else
    echo "Google Service Account ${POLICY_MANAGER_GSA_NAME} already exists."
fi

# IMPORTANT: Grant specific GCP roles needed by Policy Manager here (e.g., Secret Manager, other APIs)
# As per your note, Firestore Data Editor was removed. If Policy Manager uses Redis only, no specific
# GCP IAM roles related to data access (beyond Workload Identity User) might be needed.
# Example: gcloud projects add-iam-policy-binding "${GCP_PROJECT_ID}" --member="serviceAccount:${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" --role="roles/secretmanager.secretAccessor"

echo "Creating Kubernetes Service Account ${POLICY_MANAGER_KSA_NAME}..."
kubectl create serviceaccount "${POLICY_MANAGER_KSA_NAME}" --dry-run=client -o yaml | kubectl apply -f - || echo "KSA might already exist. Continuing..."

echo "Annotating Kubernetes Service Account ${POLICY_MANAGER_KSA_NAME}"
kubectl annotate serviceaccount "${POLICY_MANAGER_KSA_NAME}" \
    iam.gke.io/gcp-service-account="${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --overwrite

echo "Binding GSA ${POLICY_MANAGER_GSA_NAME} to KSA ${POLICY_MANAGER_KSA_NAME} for Workload Identity..."
gcloud iam service-accounts add-iam-policy-binding "${POLICY_MANAGER_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/iam.workloadIdentityUser" \
    --member="serviceAccount:${GCP_PROJECT_ID}.svc.id.goog[default/${POLICY_MANAGER_KSA_NAME}]" \
    --condition=None || echo "Binding might already exist. Continuing..."

echo "--- Workload Identity Setup for Policy Manager Complete ---"

echo "--- Setting up Workload Identity for Dashboard API ---"

DASHBOARD_API_KSA_NAME="dashboard-api-ksa"
DASHBOARD_API_GSA_NAME="dashboard-api-gsa"

echo "Creating Google Service Account ${DASHBOARD_API_GSA_NAME}..."
if ! gcloud iam service-accounts describe "${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
    gcloud iam service-accounts create "${DASHBOARD_API_GSA_NAME}" \
        --display-name="Service Account for Dashboard API"
else
    echo "Google Service Account ${DASHBOARD_API_GSA_NAME} already exists."
fi

# IMPORTANT: Grant specific GCP roles needed by Dashboard API here (e.g., BigQuery Data Viewer/Editor if ClickHouse is BigQuery, Cloud Monitoring Viewer)
# As per your note, Firestore Data Editor was removed.
# Example: gcloud projects add-iam-policy-binding "${GCP_PROJECT_ID}" --member="serviceAccount:${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" --role="roles/bigquery.dataViewer"

echo "Creating Kubernetes Service Account ${DASHBOARD_API_KSA_NAME}..."
kubectl create serviceaccount "${DASHBOARD_API_KSA_NAME}" --dry-run=client -o yaml | kubectl apply -f - || echo "KSA might already exist. Continuing..."

echo "Annotating Kubernetes Service Account ${DASHBOARD_API_KSA_NAME}"
kubectl annotate serviceaccount "${DASHBOARD_API_KSA_NAME}" \
    iam.gke.io/gcp-service-account="${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --overwrite

echo "Binding GSA ${DASHBOARD_API_GSA_NAME} to KSA ${DASHBOARD_API_KSA_NAME} for Workload Identity..."
gcloud iam service-accounts add-iam-policy-binding "${DASHBOARD_API_GSA_NAME}@${GCP_PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/iam.workloadIdentityUser" \
    --member="serviceAccount:${GCP_PROJECT_ID}.svc.id.goog[default/${DASHBOARD_API_KSA_NAME}]" \
    --condition=None || echo "Binding might already exist. Continuing..."

echo "--- Workload Identity Setup for Dashboard API Complete ---"


# --- Create Kubernetes Secrets and ConfigMaps (non-GCP managed, or API Keys) ---
echo "--- Creating Kubernetes Secrets and ConfigMaps ---"

# ClickHouse Credentials Secret
SECRET_NAME="clickhouse-credentials"
SECRET_USER="test"
SECRET_PASSWORD="test"
SECRET_CLICKHOUSE_DB="default"

echo "Checking if secret '${SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${SECRET_NAME}" \
            --from-literal=user="${SECRET_USER}" \
            --from-literal=password="${SECRET_PASSWORD}" \
            --from-literal=clickhouse_db="${SECRET_CLICKHOUSE_DB}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create ClickHouse credentials secret. Exiting."
      exit 1
    fi
    echo "Secret '${SECRET_NAME}' created."
fi

# --- LLM API Key Secrets ---

# Exit immediately if a command exits with a non-zero status.
set -e

echo "--- LLM API Key Secrets Creation ---"
echo "WARNING: API keys are hardcoded directly in this script. This is NOT recommended for production environments."
echo "         Consider using Kubernetes Secrets or a proper secrets management system (e.g., Google Secret Manager) in production."

# --- OPENAI API Key Secret ---
OPENAI_API_KEY_SECRET_NAME="openai-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
OPENAI_API_KEY_VALUE="********************************************************************************************************************************************************************"

echo "Checking if secret '${OPENAI_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${OPENAI_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${OPENAI_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${OPENAI_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${OPENAI_API_KEY_SECRET_NAME}" \
            --from-literal=OPENAI_API_KEY="${OPENAI_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create OpenAI API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${OPENAI_API_KEY_SECRET_NAME}' created."
fi

# --- GOOGLE API Key Secret ---
GOOGLE_API_KEY_SECRET_NAME="google-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
GOOGLE_API_KEY_VALUE="AIzaSyBzG0Wdhtm44BPP4Htrt739oZyNBXFZ46I"

echo "Checking if secret '${GOOGLE_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${GOOGLE_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${GOOGLE_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${GOOGLE_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${GOOGLE_API_KEY_SECRET_NAME}" \
            --from-literal=GOOGLE_API_KEY="${GOOGLE_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Google API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${GOOGLE_API_KEY_SECRET_NAME}' created."
fi

# --- ANTHROPIC API Key Secret ---
ANTHROPIC_API_KEY_SECRET_NAME="anthropic-api-key"
# !! IMPORTANT: These are PLACEHOLDER keys. REPLACE THEM WITH YOUR ACTUAL KEYS!
ANTHROPIC_API_KEY_VALUE="************************************************************************************************************"

echo "Checking if secret '${ANTHROPIC_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${ANTHROPIC_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${ANTHROPIC_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${ANTHROPIC_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${ANTHROPIC_API_KEY_SECRET_NAME}" \
            --from-literal=ANTHROPIC_API_KEY="${ANTHROPIC_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Anthropic API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${ANTHROPIC_API_KEY_SECRET_NAME}' created."
fi

# --- LLAMA3 API Key Secret ---
LLAMA3_API_KEY_SECRET_NAME="llama3-api-key"
# Placeholder for Llama3 API Key - REPLACE WITH YOUR ACTUAL KEY IF YOU HAVE ONE!
LLAMA3_API_KEY_VALUE="*************************************" # ADD YOUR ACTUAL LLAMA3 KEY HERE IF APPLICABLE

echo "Checking if secret '${LLAMA3_API_KEY_SECRET_NAME}' already exists in the default namespace..."
if kubectl get secret "${LLAMA3_API_KEY_SECRET_NAME}" -n default &> /dev/null; then
    echo "Secret '${LLAMA3_API_KEY_SECRET_NAME}' already exists. Skipping creation."
else
    echo "Secret '${LLAMA3_API_KEY_SECRET_NAME}' not found. Creating secret in the default namespace..."
    kubectl create secret generic "${LLAMA3_API_KEY_SECRET_NAME}" \
            --from-literal=LLAMA3_API_KEY="${LLAMA3_API_KEY_VALUE}" \
            -n default
    if [ $? -ne 0 ]; then
      echo "Error: Failed to create Llama3 API Key secret. Exiting."
      exit 1
    fi
    echo "Secret '${LLAMA3_API_KEY_SECRET_NAME}' created."
fi

# --- End LLM API Key Secrets ---

# Create proxy-gateway-config ConfigMap from local file
PROXY_GATEWAY_CONFIG_PATH="${SCRIPT_DIR}/proxy-gateway/config.yaml"
CONFIGMAP_NAME="proxy-gateway-config"

echo "Creating ConfigMap '${CONFIGMAP_NAME}' from ${PROXY_GATEWAY_CONFIG_PATH}..."
if [ -f "${PROXY_GATEWAY_CONFIG_PATH}" ]; then
    kubectl create configmap "${CONFIGMAP_NAME}" --from-file="${PROXY_GATEWAY_CONFIG_PATH}" --dry-run=client -o yaml | kubectl apply -f -
    if [ $? -ne 0 ]; then
        echo "Error: Failed to create ConfigMap '${CONFIGMAP_NAME}'. Exiting."
        exit 1
    fi
    echo "ConfigMap '${CONFIGMAP_NAME}' created."
else
    echo "Error: Proxy Gateway config.yaml not found at ${PROXY_GATEWAY_CONFIG_PATH}. Cannot create ConfigMap. Exiting."
    exit 1
fi

echo "--- Kubernetes Secrets and ConfigMaps Creation Complete ---"


# --- Prepare Kubernetes Manifests for Application (Perform Image Substitutions and Workload Identity modifications) ---
echo "--- Preparing Kubernetes Manifests (performing image substitutions and Workload Identity modifications) ---"

# Temporary directory for modified YAMLs
MODIFIED_MANIFESTS_DIR="${SCRIPT_DIR}/.gke_manifests"
mkdir -p "${MODIFIED_MANIFESTS_DIR}"

K8S_MANIFESTS_TO_PROCESS=(
    "${SCRIPT_DIR}/clickhouse/clickhouse-k8s.yaml"
    "${SCRIPT_DIR}/kafka/kafka-server-properties-configmap.yaml"
    "${SCRIPT_DIR}/kafka/kafka-log4j-configmap.yaml"
    "${SCRIPT_DIR}/kafka/kafka-zookeeper-k8s.yaml"
    "${SCRIPT_DIR}/redis/redis-k8s.yaml"
    "${SCRIPT_DIR}/prometheus/prometheus-k8s.yaml"
    "${SCRIPT_DIR}/mock_backend/mock-backend-gpu1.yaml"
    "${SCRIPT_DIR}/mock_backend/mock-backend-gpu2.yaml"
    "${SCRIPT_DIR}/mock_backend/mock-openai.yaml"
    "${SCRIPT_DIR}/mock_backend/mock-google.yaml"
    "${SCRIPT_DIR}/mock_backend/mock-anthropic.yaml"
    "${SCRIPT_DIR}/data-processor/data-processor.yaml"
    "${SCRIPT_DIR}/dashboard-api/dashboard-api.yaml"
    "${SCRIPT_DIR}/policy-manager/policy-manager-k8s.yaml"
    "${SCRIPT_DIR}/proxy-gateway/proxy-gateway.yaml"
    "${SCRIPT_DIR}/frontend/frontend.yaml"
    "${SCRIPT_DIR}/kafka_topic_creator/kafka-topic-creator-job.yaml"
    "${SCRIPT_DIR}/ai-optimizer/ai-optimizer.yaml"
    "${SCRIPT_DIR}/evaluation-service/evaluation-service.yaml"
    "${SCRIPT_DIR}/governance-service/governance-service.yaml"
    "${SCRIPT_DIR}/integration-service/integration-service.yaml"
)

# Enable debug logging for the loop
set -x

for manifest in "${K8S_MANIFESTS_TO_PROCESS[@]}"; do
    echo "Processing Kubernetes manifest: ${manifest}"
    if [ -f "${manifest}" ]; then
        # Copy the file to the temporary directory
        temp_manifest="${MODIFIED_MANIFESTS_DIR}/$(basename "${manifest}")"
        cp "${manifest}" "${temp_manifest}"

        # Perform image substitutions using sed on the copied file
        # Corrected substitution patterns for mock backends
        sed -i'' -e "s|image: ai-cost-performance-optimizer-mock-backend-gpu1:latest|image: ${MOCK_BACKEND_GPU1_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-mock-backend-gpu2:latest|image: ${MOCK_BACKEND_GPU2_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-mock-openai:latest|image: ${MOCK_OPENAI_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-mock-google:latest|image: ${MOCK_GOOGLE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-mock-anthropic:latest|image: ${MOCK_ANTHROPIC_IMAGE}|g" "${temp_manifest}"

        # General substitutions for other services (these patterns are confirmed correct)
        sed -i'' -e "s|image: ai-cost-performance-optimizer-proxy-gateway:latest|image: ${PROXY_GATEWAY_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-frontend:latest|image: ${FRONTEND_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-kafka-topic-creator:latest|image: ${KAFKA_TOPIC_CREATOR_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-data-processor:latest|image: ${DATA_PROCESSOR_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-dashboard-api:latest|image: ${DASHBOARD_API_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-policy-manager:latest|image: ${POLICY_MANAGER_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-ai-optimizer:latest|image: ${AI_OPTIMIZER_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-evaluation-service:latest|image: ${EVALUATION_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-governance-service:latest|image: ${GOVERNANCE_SERVICE_IMAGE}|g" "${temp_manifest}"
        sed -i'' -e "s|image: ai-cost-performance-optimizer-integration-service:latest|image: ${INTEGRATION_SERVICE_IMAGE}|g" "${temp_manifest}"

        # Workload Identity modifications for Policy Manager (remove GCP credentials, add serviceAccountName)
        if [[ "$(basename "${manifest}")" == "policy-manager-k8s.yaml" ]]; then
            echo "Modifying policy-manager-k8s.yaml for Workload Identity (removing GCP creds & injecting KSA)..."
            # Remove GCP credential environment variables if present
            sed -i'' -e '/- name: GOOGLE_APPLICATION_CREDENTIALS/,+1d' "${temp_manifest}"
            # Remove volume mounts related to GCP credentials
            sed -i'' -e '/volumeMounts:/,/volumes:/ { /volumeMounts:/! { /volumes:/! { /^\s*- name: gcp-credentials/d; /^\s*mountPath: "\/etc\/gcp"/d; /^\s*readOnly: true/d; } } }' "${temp_manifest}"
            # Remove volumes related to GCP credentials
            sed -i'' -e '/volumes:/,/spec:/ { /volumes:/! { /spec:/! { /^\s*- name: gcp-credentials/d; /^\s*secret:/d; /^\s*secretName: gcp-service-account-key/d; /^\s*items:/d; /^\s*- key: service-account.json/d; /^\s*path: service-account.json/d; } } }' "${temp_manifest}"
            # Add serviceAccountName to the Deployment spec for Workload Identity
            awk -v ksa_name="${POLICY_MANAGER_KSA_NAME}" '/containers:/ && !x { print "      serviceAccountName: " ksa_name; x=1 } { print }' "${temp_manifest}" > "${temp_manifest}.tmp" && mv "${temp_manifest}.tmp" "${temp_manifest}"
        fi

        # Workload Identity modifications for Dashboard API (remove GCP credentials, add serviceAccountName)
        if [[ "$(basename "${manifest}")" == "dashboard-api.yaml" ]]; then
            echo "Modifying dashboard-api.yaml for Workload Identity (removing GCP creds & injecting KSA)..."
            # Remove GCP credential environment variables if present
            sed -i'' -e '/- name: GOOGLE_APPLICATION_CREDENTIALS/,+1d' "${temp_manifest}"
            # Remove volume mounts related to GCP credentials
            sed -i'' -e '/volumeMounts:/,/volumes:/ { /volumeMounts:/! { /volumes:/! { /^\s*- name: gcp-credentials/d; /^\s*mountPath: "\/etc\/gcp"/d; /^\s*readOnly: true/d; } } }' "${temp_manifest}"
            # Remove volumes related to GCP credentials
            sed -i'' -e '/volumes:/,/spec:/ { /volumes:/! { /spec:/! { /^\s*- name: gcp-credentials/d; /^\s*secret:/d; /^\s*secretName: gcp-service-account-key/d; /^\s*items:/d; /^\s*- key: service-account.json/d; /^\s*path: service-account.json/d; } } }' "${temp_manifest}"
            # Add serviceAccountName to the Deployment spec for Workload Identity
            awk -v ksa_name="${DASHBOARD_API_KSA_NAME}" '/containers:/ && !x { print "      serviceAccountName: " ksa_name; x=1 } { print }' "${temp_manifest}" > "${temp_manifest}.tmp" && mv "${temp_manifest}.tmp" "${temp_manifest}"
        fi

        # --- DEBUGGING ADDITION ---
        if [[ "$(basename "${manifest}")" == "frontend.yaml" ]]; then
            echo "--- DEBUG: Contents of frontend.yaml in temp directory AFTER sed:"
            cat "${temp_manifest}"
            echo "--- DEBUG: Value of FRONTEND_IMAGE: ${FRONTEND_IMAGE}"
            echo "--- END DEBUG ---"
        fi
        # --- END DEBUGGING ADDITION ---

    else
        echo "Warning: Kubernetes manifest not found at ${manifest}. Skipping processing."
    fi
done

echo "--- All manifests prepared in ${MODIFIED_MANIFESTS_DIR} ---"

# Disable debug logging for the rest of the script
set +x

# --- Apply Kubernetes Manifests in a dependency-aware sequence ---
echo "--- Applying Kubernetes Manifests in a dependency-aware sequence ---"

echo "Ensuring kubectl context is GKE before applying manifests..."
if ! kubectl config current-context | grep -q "${GKE_CLUSTER_NAME}"; then
    echo "Error: kubectl context is not set to ${GKE_CLUSTER_NAME}. Exiting."
    exit 1
fi

# --- Stage 1: Core Data Infrastructure (Databases, Message Queues, Monitoring, Mock Backends) ---
echo "Applying core data infrastructure (ClickHouse, Kafka+Zookeeper, Redis, Prometheus, Mock Backends)..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/clickhouse-k8s.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-server-properties-configmap.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-log4j-configmap.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-zookeeper-k8s.yaml" # Deploys both Zookeeper and Kafka
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/redis-k8s.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/prometheus-k8s.yaml"

# Apply Mock Backends early as they are independent and needed by Proxy Gateway
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-backend-gpu1.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-backend-gpu2.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-openai.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-google.yaml"
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/mock-anthropic.yaml"


echo "Waiting for core infrastructure and mock backends to be available..."
kubectl wait --for=condition=Available deployment/clickhouse --timeout=600s
kubectl wait --for=condition=Available deployment/zookeeper --timeout=600s
kubectl wait --for=condition=Available deployment/kafka --timeout=600s
kubectl wait --for=condition=Available deployment/redis --timeout=600s
kubectl wait --for=condition=Available deployment/prometheus --timeout=600s
kubectl wait --for=condition=Available deployment/mock-backend-gpu1 --timeout=600s
kubectl wait --for=condition=Available deployment/mock-backend-gpu2 --timeout=600s
kubectl wait --for=condition=Available deployment/mock-openai --timeout=600s
kubectl wait --for=condition=Available deployment/mock-google --timeout=600s
kubectl wait --for=condition=Available deployment/mock-anthropic --timeout=600s
echo "Core infrastructure and mock backend deployments are ready."

# --- Stage 2: Kafka Topic Creation ---
echo "Applying Kafka Topic Creator Job..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/kafka-topic-creator-job.yaml"

echo "Waiting for Kafka Topic Creator Job to complete..."
kubectl wait --for=condition=complete job/kafka-topic-creator --timeout=600s
echo "Kafka topics created."

# --- Stage 3: Data Processor Deployment ---
if [ "${_BUILD_DATA_PROCESSOR}" = "true" ]; then
  echo "Applying Data Processor deployment..."
  kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/data-processor.yaml"
fi

echo "Waiting for Data Processor to be available..."
kubectl wait --for=condition=Available deployment/data-processor --timeout=600s

# Verify that the Data Processor has created the inference_logs table
echo "Verifying that the inference_logs table has been created in ClickHouse..."
CLICKHOUSE_POD=$(kubectl get pod -l app=clickhouse -o jsonpath="{.items[0].metadata.name}" 2>/dev/null)
if [ -z "$CLICKHOUSE_POD" ]; then
  echo "Error: ClickHouse pod not found. Cannot verify table creation."
  exit 1
fi

# Wait for the inference_logs table to be created
MAX_RETRIES=30
RETRY_COUNT=0
TABLE_EXISTS=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$TABLE_EXISTS" = false ]; do
  echo "Checking if inference_logs table exists (attempt $((RETRY_COUNT + 1))/${MAX_RETRIES})..."
  if kubectl exec "$CLICKHOUSE_POD" -- clickhouse-client -q "EXISTS TABLE inference_logs;" 2>/dev/null | grep -q "1"; then
    TABLE_EXISTS=true
    echo "Table 'inference_logs' exists in ClickHouse."
  else
    echo "Table 'inference_logs' not found yet. Waiting 10 seconds before retry..."
    sleep 10
    RETRY_COUNT=$((RETRY_COUNT+1))
  fi
done

if [ "$TABLE_EXISTS" = false ]; then
  echo "Error: inference_logs table was not created within the expected time."
  echo "Checking Data Processor logs for errors..."
  DATA_PROCESSOR_POD=$(kubectl get pod -l app=data-processor -o jsonpath="{.items[0].metadata.name}" 2>/dev/null)
  if [ -n "$DATA_PROCESSOR_POD" ]; then
    kubectl logs "$DATA_PROCESSOR_POD"
  else
    echo "Data Processor pod not found."
  fi
  exit 1
fi

echo "Data Processor is ready and has created the inference_logs table."

# --- Stage 4: Reset Kafka Offset ---
echo "Executing Kafka offset reset for data-processor-group..."
KAFKA_POD=$(kubectl get pod -l app=kafka -o jsonpath='{.items[0].metadata.name}' || true)
if [ -z "$KAFKA_POD" ]; then
    echo "Warning: Kafka pod not found, cannot reset offsets. Make sure Kafka is running."
else
    kubectl exec "$KAFKA_POD" -- /usr/bin/kafka-consumer-groups \
        --bootstrap-server localhost:9092 \
        --group data-processor-group \
        --topic inference-logs \
        --reset-offsets --to-earliest --execute || echo "Warning: Kafka offset reset failed. Continuing."
    echo "Kafka offset reset completed for data-processor-group on inference-logs."
fi

# --- Stage 5: AI Optimizer Deployment ---
echo "Applying AI Optimizer deployment (including SA, Role, RoleBinding)..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/ai-optimizer.yaml"

echo "Waiting for AI Optimizer to be available..."
kubectl wait --for=condition=Available deployment/ai-optimizer --timeout=600s
echo "AI Optimizer is ready."

# --- Stage 6: Other Dependent Applications (APIs, Gateways, Frontends) ---
# Deploy Policy Manager first as it's a dependency for frontend Nginx
if [ "${_DEPLOY_POLICY_MANAGER}" = "true" ]; then
  echo "Applying Policy Manager deployment..."
  kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/policy-manager-k8s.yaml"
fi

echo "Waiting for Policy Manager to be available..."
kubectl wait --for=condition=Available deployment/policy-manager --timeout=600s
echo "Policy Manager is ready."

echo "Applying Evaluation Service deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/evaluation-service.yaml"

echo "Waiting for Evaluation Service to be available..."
kubectl wait --for=condition=Available deployment/evaluation-service --timeout=600s
echo "Evaluation Service is ready."

# --- REDIS POPULATOR JOB ---
echo "--- Starting Redis Populator Job ---"
# Set environment variables required by create_redis_populator_job.sh
# These are already exported at the top of this script, so they are available
# to subprocesses like create_redis_populator_job.sh.
# The script itself uses the ARTIFACT_REGISTRY_REPO variable, which is propagated.
# Execute the create_redis_populator_job.sh script
# This script itself will create the job and wait for its completion.
if [ "${_DEPLOY_REDIS_POPULATOR_JOB}" = "true" ]; then
  "${SCRIPT_DIR}/redis-populator-job/create_redis_populator_job.sh"
fi

echo "--- Redis Populator Job creation (within New_Autostart.sh) complete ---"

# --- RESTART POLICY MANAGER & AI OPTIMIZER AFTER REDIS POPULATOR ---
# This ensures policy-manager re-reads the Redis data with the correct API keys.
echo "--- Restarting Policy Manager to pick up updated API keys from Redis ---"
kubectl rollout restart deployment/policy-manager
echo "Policy Manager rollout restart initiated. Waiting for it to be ready..."
kubectl wait --for=condition=Available deployment/policy-manager --timeout=300s
echo "Policy Manager restarted and is ready."

# This ensures ai-optimizer re-reads the Redis data with the correct policies.  
echo "--- Restarting AI Optimizer to pick up updated policies from Redis ---"
kubectl rollout restart deployment/ai-optimizer
echo "AI Optimizer rollout restart initiated. Waiting for it to be ready..."
kubectl wait --for=condition=Available deployment/ai-optimizer --timeout=300s
echo "AI Optimizer restarted and is ready."

echo "Applying Dashboard API deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/dashboard-api.yaml"

echo "Applying Proxy Gateway deployment..."
kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/proxy-gateway.yaml"

echo "Applying Frontend Dashboard deployment..."
# The frontend's LoadBalancer service IP is determined during the Cloud Build,
# and then the frontend Docker image is rebuilt with this IP injected into its config.js.
# This initial apply ensures the LoadBalancer service is created so an IP can be assigned.
# The cloudbuild.yaml will then re-apply the frontend deployment after the image rebuild.
if [ "${_DEPLOY_FRONTEND}" = "true" ]; then
  kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/frontend.yaml"
fi

echo "Waiting for remaining deployments to be available..."
kubectl wait --for=condition=Available deployment/dashboard-api --timeout=600s
kubectl wait --for=condition=Available deployment/proxy-gateway --timeout=600s
kubectl wait --for=condition=Available deployment/frontend-dashboard --timeout=600s
echo "All remaining deployments are ready."


#echo "Waiting for remaining deployments to be available..."
#kubectl wait --for=condition=Available deployment/governance-service --timeout=600s
#kubectl wait --for=condition=Available deployment/integration-service --timeout=600s
#echo "All remaining deployments are ready."

#kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/governance-service.yaml"
#kubectl apply -f "${MODIFIED_MANIFESTS_DIR}/integration-service.yaml"

echo "--- Kubernetes Manifest Application Complete ---"

echo "--- Setup Complete ---"
echo "Your application components are being deployed to GKE."
echo "You can check the status of your pods with: kubectl get pods"
echo "You can check the status of your services with: kubectl get services"

echo "To access services exposed via LoadBalancer (e.g., proxy-gateway, frontend-dashboard, prometheus):"
echo "Run: kubectl get services -o wide"
echo "Look for the EXTERNAL-IP of the desired service. It might take a few minutes for the IP to provision."

echo "Script execution finished."
